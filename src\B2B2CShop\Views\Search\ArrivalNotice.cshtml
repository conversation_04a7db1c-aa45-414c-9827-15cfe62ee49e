﻿@{
    Layout = "_RootNoHeader";
}
<style>
    h3{
        margin:10px;
    }
    .hint{
        padding: 10px 0px 15px 20px;
        background-color: #F5F5F5;
        width:350px;
        margin:0px 0px 15px 40px;
    }
</style>
<div class="layui-form" style="margin-left:25px" >
    <h3>@T("到货通知")</h3>
    <div class="hint">@T("当商品到货时，我们会通过邮件通知您哦~")</div>
    <div class="layui-form-item">
        <label class="layui-form-label">@T("邮箱地址")</label>
        <div class="layui-input-inline" style="width:300px">
            <input type="text" name="mail" placeholder="@T("请输入邮箱地址")" autocomplete="off" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">@T("地址信息")</label>
        <div class="layui-input-block" style="width:300px">
            <select name="address">
                <option>@T("请选择地址信息")</option>
                @foreach (var item in Model.AddressList)
                {
                    <option>@item.AreaInfo @item.AddressDetail</option>
                }
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-input-block" style="width:300px;color:#2C79E8">
            <a href="javascript:openAddress()">@T("使用新地址")</a>
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-input-block" style="width:100px;padding-left:80px">
            <button type="submit" class="layui-btn" lay-submit>@T("确定")</button>
        </div>
    </div>
</div>
<script asp-location="Footer">

    function openAddress(){
        parent.openLayerB();
    }

    layui.use(['form'], function () {
        var form = layui.form;

        form.render();

        form.on('submit', function (data) {
            console.log(data.field);
        });
    });
</script>
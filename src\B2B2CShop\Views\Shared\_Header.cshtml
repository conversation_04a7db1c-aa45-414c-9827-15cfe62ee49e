@using B2B2CShop.Entity
@inject IManageProvider _provider
@inject IWebHelper _webHelper
@inject IWorkContext workContext
@{
    // Css
    PekHtml.AppendCssFileParts("~/public/css/header.css");
    // PekHtml.AppendCssFileParts("~/public/modules/layui/css/layui.css");

    var user = _provider.TryLogin(Context);

    var returnUrl = _webHelper.GetRawUrl(Context.Request);

    var site = SiteInfo.GetDefaultSeo();

    var InfoList =  GoodsClass.FindAllByTreeLan(workContext.WorkingLanguage.Id);

    var cartCount = Cart.FindAllByBuyerId(SId).Count();

}
<style asp-location="true">
    .displayFlex {
    display: flex !important;
    }
</style>
<script asp-location="Head">
    var table = layui.table;
    var layer = layui.layer;
    var form = layui.form;
    var dropdown = layui.dropdown;
    var dialog = layui.dialog;
</script>
<!-- 头部开始  -->
<div class="header">
    <a href="/Index.html"><img src="~/public/images/icons/hlk.png" style="object-fit: contain;width: 260px;" class="headLogo"></a>
    <div class="headerItem searchBoxItem" >
        <div class="searchBox">
            <select class="" name="select" id="id_select2_demo1" style="min-width: 100px;" title="@T("分类")">
                <option value="1">@T("商品")</option>
                <option value="2" selected>@T("供应商")</option>
            </select>


            <span class="line"> </span>

            <input class="searchInput" placeholder="@T("请输入产品规格")/@T("型号")/@T("品牌")/@T("商家")" id="headerkey">
            <button class="searchButton flex" onclick="Search()">
                <div class="iconfont icon-search" style="margin-right: 4px;font-size: 20px;"></div>
                @T("搜索")
            </button>
            <!--  推荐关键字  -->
            <div class="header_keyBox flex">
                @{
                    var Lid = Language?.Id??1;
                    var list = SearchKey.FindAllEnabledLan(Lid);
                    foreach (var item in list)
                    {
                        <input class="header_keyCheck" type="radio" id="@item.Id" name="menuOptions">
                        <label class="header_key textOver" style="color:@item.Color" onclick="Search2(@item.Name)">@item.Name</label>
                    }
                }
            </div>
        </div>
    </div>
    <div class="headerItem" style="width: 150px;">
        <div class="flex textOver">
            <input type="checkbox" name="" id="goods1">
            <label for="goods1" style="margin: -4px 5px;">@T("库存量")</label>
        </div>
        <div class="flex textOver" style="margin-left: auto;">
            <input type="checkbox" name="" id="goods2">
            <label for="goods2" style="margin: -4px 5px;">@T("RoSH")</label>
        </div>
    </div>
    @* 修改后 *@
    <div class="headerItem">
       <div class="currency" >
            <img src="@Currency?.ImgPath" style="width: 1.5vw;object-fit: contain;" id="currencyImage">
            <span class="lngText" onclick="showLngBoxFn()" id="h">
                <span>@Language?.UniqueSeoCode.ToUpper()</span><span class="bias">/</span> <br><span>
                    @Currency?.Code
                    <i class="iconfont icon-xiangxia"></i></span>
        </div>
        <!-- 语言切换 & 货币切换盒子 -->
        <div class="showLngBox" id="showLngBox" data-show="false" style="display: none;">
            <div style="font-size: 12px;">@T("更改语言")</div>
            <div class="dropdown1">
                <div class="selected1" id="selected1" style="height: 20px;">
                    <span id="selected-text1">@Language?.DisplayName</span>
                    <div class="arrow1"></div>
                </div>
                <div class="options1" id="options1">

                    @foreach (var item in LanguageList)
                    {
                        <div class="option" data-value="@(item.UniqueSeoCode)" onclick="toRouter(this)"
                            data-link='@Url.RouteUrl("ChangeLanguage", new { langid = item.Id, returnUrl })'>
                            <span>@item.DisplayName</span>
                        </div>
                    }
                </div>
            </div>
            <div style="font-size: 12px;">@T("更改货币")</div>
            <div class="dropdown">
                <div class="selected" id="selected">
                    <div>
                    <img src="@Currency?.ImgPath" alt="" id="selected-img">
                    <span>@Currency?.Code @Currency?.SymbolLeft</span>
                    </div>
                    <div class="arrow"></div>
                </div>
                <div class="options" id="options">
                    @foreach (var item in CurrencyList)
                    {
                        <div class="option" data-value="@(item.Code)" onclick="toRouter(this)"
                            data-link='@Url.RouteUrl("ChangeCurrency", new { currencyid = item.Id, returnUrl })'>
                            <img src="@item?.ImgPath" alt="">
                            <a title="@item.Name">@item?.Code @item?.SymbolLeft</a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
    @* 修改后 *@
    <div class="headerItem acountBox">
        
        @{
            if (user != null && user.Enable)
            {
                <!-- 登录后 -->
                <div class="flex" style="white-space: nowrap;">
                    <i class="iconfont icon-weidenglu icon" style="font-size: 1.4em !important;margin-right: 5px;"></i>
                    <a href="@Url.Action("Index", "Account", new { area = "Member" })"><span style="cursor: default;">@T("账号信息")</span></a>
                </div>
            }
        }

        @{
            if (user == null || !user.Enable)
            {
                <!-- 未登录 -->
                <div class="flex" style="font-size: 14px;">
                    <i class="iconfont icon-weidenglu" style="font-size: 1.8em !important;margin: -3px 3px 0 0;"></i>
                    <div class="hoverText textOver">
                        <a class="hoverBlue" href="@_PekUrlHelper.PekRouteUrl(Url, "MuliLogin", Language?.UniqueSeoCode)">@T("登录")</a>
                    </div>/
                    <div class="hoverText textOver hoverBlue">
                        <a class="hoverBlue" href="@_PekUrlHelper.PekRouteUrl(Url, "MuliRegister", Language?.UniqueSeoCode)">@T("注册")</a>
                    </div>
                </div>
            }
        }

        <div class="acountInfo">
            @{
                if (user != null && user.Enable)
                {
                    <!-- 登录后 -->
                    <div class="loginOut">
                        <a href="@Url.Action("Index", "Logout", new { Area = "" })">@T("退出")</a>
                    </div>
                    <div class="acountHead flex" data-login_flex="true" style="padding-top: 0;margin-top: 0;">
                        <a href="@Url.Action("Index", "Account", new { area = "Member" })" class="" style="display: block;">
                            <img src="~/public/images/icons/morentouxiang.png">
                        </a>
                        <div class="" style="margin-left: 5px;">
                            <div class="flex">
                                <div class="textOver" style="max-width: 80px;">
                                    <!-- 是这里 -->
                                    @if (ValidateHelper.IsMobile(user.Name))
                                    {
                                        <a href="@Url.Action("Index", "Account", new { area = "Member" })" class="textSelect"> @DesensitizedUtil.Mobile(user.Name); </a>

                                    }
                                    else if (ValidateHelper.IsEmail(user.Name))
                                    {
                                        <a href="@Url.Action("Index", "Account", new { area = "Member" })" class="textSelect"> @DesensitizedUtil.Email(user.Name); </a>

                                    }
                                    else
                                    {
                                        <a href="@Url.Action("Index", "Account", new { area = "Member" })" class="textSelect"> @DesensitizedUtil.ReplaceWithSpecialChar(user.Name); </a>

                                    }

                                </div>
                                <div style="margin-left: auto;font-size: 14px;white-space: nowrap;">@T("您好！")</div>
                            </div>
                            <div class="welcome" >@T("欢迎来到{0}", site.SiteName!)</div>
                        </div>
                    </div>
                }

                if (user == null || !user.Enable)
                {
                    <!-- 未登录 -->
                    <div class="loginOutBox" data-login_flex="true">
                        <a style=" background-color: var(--blue-deep);
                            border: 1px solid var(--line);color: white !important;" href="@_PekUrlHelper.PekRouteUrl(Url, "MuliLogin", Language?.UniqueSeoCode)">@T("登录")</a>
                        <a href="@_PekUrlHelper.PekRouteUrl(Url, "MuliRegister", Language?.UniqueSeoCode)">@T("注册")</a>
                        @* <div style=" background-color: var(--blue-deep);
                        border: 1px solid var(--line);">
                        <a href="@Url.Action("Index", "Login", new { area = "" })" style="color: white;">@T("登录")</a> </div>
                    <div> <a href="@Url.Action("Index", "Register", new { area = "" })">@T("注册")</a> </div> *@
                    </div>
                }
            }

            <div class="acountArea" style="border-top: 1px solid var(--line);padding-top: 5px;">
                <div onclick="(this)">
                    <div class="iconfont icon-weidenglu icon @(user == null || !user.Enable?"unloginText":"")"></div>
                    <div><a @(user == null || !user.Enable ? " class=unloginText " : " href=/Member/Account/Index ")>@T("账号信息")</a></div>
                </div>
                <div onclick="(this)">
                    <div class="iconfont icon-a-description2x icon @(user == null || !user.Enable?"unloginText":"")"></div>
                    <div><a @(user == null || !user.Enable ? " class=unloginText " : " href=/Member/Orders/Index")>@T("订单")</a></div>
                </div>
                <div onclick="(this)">
                    <div class="iconfont icon-wuliu icon @(user == null || !user.Enable?"unloginText":"")"></div>
                    <div class="textOver"><a @(user == null || !user.Enable ? " class=unloginText " : " href=/Member/Refund/Index ")>@T("退货和退款")</a></div>
                </div>
                @*<div onclick="(this)">
                    <div class="iconfont icon-a-cartline icon @(user == null || !user.Enable?"unloginText":"")"></div>
                    <div><a @(user == null || !user.Enable ? " class=unloginText " : " href=javascript:; ")>@T("购物车")</a></div>
                </div>*@
                <div onclick="(this)">
                    <div class="iconfont icon-heart icon @(user == null || !user.Enable?"unloginText":"")"></div>
                    <div><a @(user == null || !user.Enable ? " class=unloginText " : " href=/Member/Wish/Index ")>@T("心愿清单")</a></div>
                </div>
            </div>
            <div class="acountArea">
                @*<div onclick="(this)">
                    <div class="iconfont icon-xiaoxitongzhi icon @(user == null || !user.Enable?"unloginText":"")"></div>
                    <div class="textOver"><a @(user == null || !user.Enable ? " class=unloginText " : " href=/Member/Message/Index ")>@T("信息中心")</a></div>
                </div>*@
                <div onclick="(this)">
                    <div class="iconfont icon-edit icon @(user == null || !user.Enable?"unloginText":"")"></div>
                    <div><a @(user == null || !user.Enable ? " class=unloginText " : " href=/Member/Evaluate/Index ")>@T("评价")</a></div>
                </div>
                <div onclick="(this)">
                    <div class="iconfont icon-wuliuxinxi icon @(user == null || !user.Enable?"unloginText":"")"></div>
                    <div><a @(user == null || !user.Enable ? " class=unloginText " : " href=/Member/Invoice/Index")>@T("发票")</a></div>
                </div>
            </div>
            <div class="acountArea" style="border: none;padding-bottom: 0px;">
                <div onclick="(this)">
                    <div class="iconfont icon-shezhi2 icon @(user == null || !user.Enable?"unloginText":"")"></div>
                    <div><a @(user == null || !user.Enable ? " class=unloginText " : " href=/Member/Setting/Index ")>@T("账户设置")</a></div>
                </div>
                <div onclick="(this)">
                    <div class="iconfont icon-dianhua icon @(user == null || !user.Enable?"unloginText":"")"></div>
                    <div><a @(user == null || !user.Enable ? " class=unloginText " : " href=/Member/Contact/Index ")>@T("联系方式")</a></div>
                </div>
                @*<div onclick="(this)">
                    <div class="iconfont icon-creditcard icon @(user == null || !user.Enable?"unloginText":"")"></div>
                    <div><a @(user == null || !user.Enable ? " class=unloginText " : " href=/Member/PaymentMethod/Index ")>@T("支付方式")</a></div>
                </div>*@
            </div>
        </div>
    </div>
    <div class="headerItem cartBox" onclick="window.location.href='@Url.Action("Index","ShoppingCart",new {Area = ""})'" >
        <div class="cart-count" id="cartCount" style="@(cartCount <= 0 ? "display:none;" : "")">@cartCount</div>
        <i class="iconfont icon-cart icon" style="font-size: 1.4em;margin-right: .2em;"></i>
        <a href="@Url.Action("Index","ShoppingCart",new {Area = ""})"><span class="hoverText textOver">@T("购物车")</span></a>
    </div>
</div>
<!-- 头部分类 -->
<div class="header2">
    <div class="flex productCategory" data-select="false">
        <label for="select" class="iconfont icon-fenlei2" style="font-size: 26px;">
        </label> @T("产品分类") <i class="iconfont icon-xiangxia"></i>
        @* <div class="productCategoryBox">
            <!-- 一级页面 -->
            <div class="productCategory_onePage">
                @foreach (var item in InfoList)
                {
                    <div data-select="false" id="@("e"+item.Id)">
                        <a href="/ProductClass/Index?CId=@item.Id"><span style="width: 80%;" class="textOver">@item.Name</span> </a>
                        <i class="iconfont icon-jiantou2 rightIcon"></i>
                    </div>
                }
            </div>
            @foreach (var item1 in InfoList)
            {
                <div class="productCategory_hoverPages" id="@("ce"+item1.Id)">
                    <div class="twoPage" id="@("d"+item1.Id)">
                        @foreach (var item2 in item1.Child??new List<B2B2CShop.Dto.GoodsClassDto1>())
                        {
                            <div data-select="false">
                                <a href="/Products/Index?page=1&limit=3&cId=@item2.Id"><span style="width: 80%;" class="textOver">@item2.Name</span></a>
                            </div>
                        }
                    </div>
                    <div class="threePage">
                        <p class="goodsMore"><a href="#">更多<i class="iconfont icon-jiantou2 rightIcon"></i></a></p>
                        @foreach (var item2 in item1.Goods??new List<B2B2CShop.Dto.GoodsDto>())
                        {
                            <a class="jump"href="@Url.Action("Index","Goods",new {Area = ""})?goodsId=@item2.Id">
                                <div class="threePage_contentBox" title="@item2.Name">
                                    <div class="threePage_contentBox_name">
                                       @item2.Name (@item2.GoodsStorage)
                                    </div>
                                </div>
                            </a>
                        }
                    </div>
                </div>
            }
            <!--  -->
        </div> *@
        <div class="productCategoryBox">
            <!-- 一级页面 -->
            <div class="productCategory_onePage">
                @foreach (var item in InfoList)
                {
                    <div data-select="false" id="@("e"+item.Id)">
                        <a href="/ProductClass/Index?CId=@item.Id"><span style="width: 80%;" class="textOver">@item.Name</span> </a>
                        <i class="iconfont icon-jiantou2 rightIcon"></i>
                    </div>
                }
            </div>
            @foreach (var item1 in InfoList)
            {
                <div class="productCategory_hoverPages" id="@("ce"+item1.Id)">
                    <div class="twoPage" id="@("d"+item1.Id)">
                        @foreach (var item2 in item1.Child??new List<B2B2CShop.Dto.GoodsClassDto1>())
                        {
                            <div data-select="false">
                                <a href="/Products/Index?page=1&limit=3&cId=@item2.Id"><span style="width: 80%;" class="textOver">22 @item2.Name</span>
                                <i class="iconfont icon-jiantou2 rightIcon"></i>
                                </a>
                                <div class="threePage">
                        <p class="goodsMore"><a href="#">更多<i class="iconfont icon-jiantou2 rightIcon"></i></a></p>
                       
                            <a class="jump"href="@Url.Action("Index","Goods",new {Area = ""})?goodsId=@item2.Id">
                                <div class="threePage_contentBox" title="@item2.Name">
                                    <div class="threePage_contentBox_name">
                                       @item2.Name
                                    </div>
                                </div>
                            </a>
                        
                    </div> 
                            </div>
                        }
                    </div>
                    @* <div class="threePage">
                        <p class="goodsMore"><a href="#">更多<i class="iconfont icon-jiantou2 rightIcon"></i></a></p>
                        @foreach (var item2 in item1.Goods??new List<B2B2CShop.Dto.GoodsDto>())
                        {
                            <a class="jump"href="@Url.Action("Index","Goods",new {Area = ""})?goodsId=@item2.Id">
                                <div class="threePage_contentBox" title="@item2.Name">
                                    <div class="threePage_contentBox_name">
                                       @item2.Name (@item2.GoodsStorage)
                                    </div>
                                </div>
                            </a>
                        }
                    </div> *@
                </div>
            }
            <!--  -->
        </div>
    </div>
    <span class="line2" style="height: 17px;margin-left: 10px;"></span>
    <div>
        <a class="header2_key" href="/">@T("首页")</a>
    </div>
    <!-- 第一版 -先干到海凌科品牌商去 -->
    <div>
        <a class="header2_key" href="@Url.Action("Index","Supplier")">@T("供应商")</a>
    </div>

    <div>
        <a class="header2_key" href="@_PekUrlHelper.PekRouteUrl(Url, "MuliHelpHome", Language?.UniqueSeoCode)">@T("帮助中心")</a>
    </div>

        @*<div>
        <a class="header2_key" href="@Url.Action("Index", "Bom")">@T("Bom Tool")</a>
    </div>
    <div>
        <a class="header2_key">@T("RFQ")</a>
    </div>
    <div>
        <a class="header2_key">@T("技术资源")</a>
    </div>*@
    @* <div class="flex" id="isPopup">
        <div class="lang">
        @T("语言")：<span class="lngText" onclick="showLngBoxFn()" id="l">@Language?.DisplayName</span>
        </div>
        <div class="currency" >
            @T("货币")：
            <img src="@Currency?.ImgPath" style="width: 1.5vw;object-fit: contain;" id="currencyImage">
            <span class="lngText" onclick="showLngBoxFn()" id="h"
            style="text-decoration: underline;">@Currency?.Code @Currency?.SymbolLeft</span>
        </div>
        <!-- 语言切换 & 货币切换盒子 -->
        <div class="showLngBox" id="showLngBox" data-show="false" style="display: none;">
            <div style="font-size: 12px;">@T("更改语言")</div>
            <div class="dropdown1">
                <div class="selected1" id="selected1" style="height: 20px;">
                    <span id="selected-text1">@Language?.DisplayName</span>
                    <div class="arrow1"></div>
                </div>
                <div class="options1" id="options1">
                    
                    @foreach (var item in LanguageList) 
                    {
                        <div class="option" data-value="@(item.UniqueSeoCode)" onclick="toRouter(this)" data-link='@Url.RouteUrl("ChangeLanguage", new { langid = item.Id, returnUrl })'>
                            <span>@item.DisplayName</span>
                        </div>
                    }
                </div>
            </div>
            <div style="font-size: 12px;">@T("更改货币")</div>
            <div class="dropdown">
                <div class="selected" id="selected">
                    <img src="@Currency?.ImgPath" alt="" id="selected-img">
                    <span>@Currency?.Code @Currency?.SymbolLeft</span>
                    <div class="arrow"></div>
                </div>
                <div class="options" id="options">
                    @foreach (var item in CurrencyList) {
                        <div class="option" data-value="@(item.Code)" onclick="toRouter(this)" data-link='@Url.RouteUrl("ChangeCurrency", new { currencyid = item.Id, returnUrl })'>
                            <img src="@item?.ImgPath" alt="">
                            <a title="@item.Name">@item?.Code @item?.SymbolLeft</a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div> *@
</div>
<style>
.welcome{
 margin: 0.2vw 0 0 0;
 white-space: wrap;
 font-size: 14px;
}
/* 购物车数量样式 */
.cart-count {
    position: absolute;
    top: 1px;
    left: 12px;
    background-color: red;
    color: white;
    font-size: 12px;
    min-width: 18px;
    height: 18px;
    border-radius: 9px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.dropdown, .dropdown1 {
    position: relative;
    width: 180px;
}

.dropdown .selected, .dropdown1 .selected1 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px;
    border: 1px solid #ccc;
    cursor: pointer;
    background-color: #fff;
    transition: border-color 0.3s;
}

.dropdown .selected:hover, .dropdown1 .selected1:hover {
    border-color: #999;
}

.dropdown .selected img, .dropdown1 .selected1 img {
    width: 20px;
    height: 20px;
    margin-right: 4px;
}

.dropdown .selected .arrow, .dropdown1 .selected1 .arrow1 {
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #999;
    transition: transform 0.3s;
}

.dropdown .selected.open .arrow, .dropdown1 .selected1.open .arrow1 {
    transform: rotate(180deg);
}

.dropdown .options, .dropdown1 .options1 {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    border: 1px solid #ccc;
    background-color: #fff;
    z-index: 1000;
    max-height: 150px;
    overflow-y: auto;
    transition: opacity 0.3s ease-in-out;
    opacity: 0;
}

.dropdown .options.active, .dropdown1 .options1.active {
    display: block;
    opacity: 1;
}

.dropdown .option, .dropdown1 .option {
    display: flex;
    align-items: center;
    padding: 10px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
}

.dropdown .option:last-child, .dropdown1 .option:last-child {
    border-bottom: none;
}

.dropdown .option img, .dropdown1.option img {
    width: 20px;
    height: 20px;
    margin-right: 10px;
}

.dropdown .option:hover, .dropdown1 .option:hover {
    background-color: #f0f0f0;
}
</style>
<script>
    var jQuery = $;
     // 监听鼠标hover事件
    var productCategoryBox_dom = $(".productCategoryBox");
    productCategoryBox_dom.mouseover(function (e) {
        if (e.target.getAttribute("data-select")) {
            $(e.target).siblings().attr("data-select", "false");
            e.target.setAttribute("data-select", "true");
        }
    })
    // 加载图片 - 将data-src的值赋给src属性
    var productCategory_onePage_dom = $('.productCategory_onePage');
    var moveInArr = []
    productCategory_onePage_dom.mouseover(function (e) {
        var targetId = e.target.id;
        if (!targetId || moveInArr.includes(targetId)) return;
        
        moveInArr.push(e.target.id);
        // 找到对应的二级菜单容器
        var correspondingMenuId = "c" + targetId;
        var correspondingMenu = $("#" + correspondingMenuId);
        
        // 加载该容器内所有懒加载图片
        setTimeout(function() {
            correspondingMenu.find('img[data-src]').each(function() {
                if (!$(this).attr('src') || $(this).attr('src') === '') {
                    $(this).attr('src', $(this).data('src'));
                }
            });
        }, 100); // 添加小延迟，确保用户真的想查看分类
    })
     $('#id_select2_demo1').select2({
         placeholder: '请选择'
     });
    const selected = document.getElementById('selected');
    const selected1 = document.getElementById('selected1');
    const options = document.getElementById('options');
    const options1 = document.getElementById('options1');
    // 监听全局点击事件
    document.addEventListener('click', function(event) {
        const isPopup = document.getElementById('isPopup');
        const showLngBox = document.getElementById('showLngBox');
        const languageToggleButton = document.getElementById('h'); // 语言切换按钮

        // 安全检查 isPopup 是否存在
        if (isPopup && isPopup.contains(event.target)) {
            return;
        }

        // 检查是否点击了语言切换按钮或其子元素
        if (languageToggleButton && (event.target === languageToggleButton || languageToggleButton.contains(event.target))) {
            return; // 如果点击的是语言切换按钮，不执行隐藏逻辑
        }

        // 检查是否点击了语言框内部
        if (showLngBox && showLngBox.contains(event.target)) {
            return; // 如果点击的是语言框内部，不隐藏
        }

        // 如果点击了外部区域且语言框是显示状态，则隐藏它
        if (showLngBox && showLngBox.dataset.show === 'true') {
            showLngBoxFn()
        }

        // 安全检查所有元素是否存在
        if (selected && options && !selected.contains(event.target) && !options.contains(event.target)) {
            options.classList.remove('active');
            selected.classList.remove('open');
        }

        if (selected1 && options1 && !selected1.contains(event.target) && !options1.contains(event.target)) {
            options1.classList.remove('active');
            selected1.classList.remove('open');
        }
    });

 
    const selectedImg = document.getElementById('selected-img');
    const selectedText = document.getElementById('selected-text');
    const selectedText1 = document.getElementById('selected-text1');
    const arrow = selected ? selected.querySelector('.arrow') : null;
    const arrow1 = selected1 ? selected1.querySelector('.arrow1') : null;

    // 为 selected 添加事件监听器（如果元素存在）
    if (selected && options && options1 && selected1) {
        selected.addEventListener('click', () => {
            options.classList.toggle('active');
            selected.classList.toggle('open');
            options1.classList.remove('active');
            selected1.classList.remove('open');
        });
    }

    // 为 selected1 添加事件监听器（如果元素存在）
    if (selected1 && options1 && options && selected) {
        selected1.addEventListener('click', () => {
            options1.classList.toggle('active');
            selected1.classList.toggle('open');
            options.classList.remove('active');
            selected.classList.remove('open');
        });
    }

    var lCode = null
    var hCode = null
    var isSelL = false
    var isSelH = false

    // 为 options 添加事件监听器（如果元素存在）
    if (options) {
        options.addEventListener('click', (e) => {
            const option = e.target.closest('.option');
            if (option) {
                const imgSrc = option.querySelector('img').src;
                const text = option.querySelector('span').textContent;
                hCode = option.dataset.value
                isSelH = true
                if (selectedImg) selectedImg.src = imgSrc;
                if (selectedText) selectedText.textContent = text;
                options.classList.remove('active');
                if (selected) selected.classList.remove('open');
            }
        });
    }

    // 为 options1 添加事件监听器（如果元素存在）
    if (options1) {
        options1.addEventListener('click', (e) => {
            const option = e.target.closest('.option');
            if (option) {
                const text = option.querySelector('span').textContent;
                lCode = option.dataset.value
                isSelL = true
                if (selectedText1) selectedText1.textContent = text;
                options1.classList.remove('active');
                if (selected1) selected1.classList.remove('open');
            }
        });
    }
</script>
<script asp-location="Footer">
        // 页面加载时填充搜索框内容
        $(document).ready(function() {
            // 获取URL参数
            function getUrlParameter(name) {
                name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
                var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
                var results = regex.exec(location.search);
                return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
            }

            // 获取搜索关键字
            var searchKey = getUrlParameter('headerkey');
            if (searchKey && searchKey !== "") {
                // 填充搜索框
                $("#headerkey").val(searchKey);
            }

            // 设置复选框状态
            var inventory = getUrlParameter('inventory');
            if (inventory === 'true') {
                $("#goods1").prop("checked", true);
            }

            var roSh = getUrlParameter('roSh');
            if (roSh === 'true') {
                $("#goods2").prop("checked", true);
            }

            // 设置下拉框状态
            var selectVal = getUrlParameter('Keys') ? 2 : 1;
            $("#id_select2_demo1").val(selectVal).trigger('change');
            // 监听搜索框回车事件
            $('#headerkey').on('keydown', function (e) {
                if (e.key === 'Enter' || e.keyCode === 13) {
                    e.preventDefault(); // 阻止表单默认提交
                    Search();
                }
            });
        });

        // 监听鼠标hover事件
        var mainBox1_left_dom1 = $(".productCategoryBox");
        mainBox1_left_dom1.mouseover(function (e)
        {

              if (e.target.getAttribute("data-select"))
              {
                    $(e.target).siblings().attr("data-select", "false");
                    e.target.setAttribute("data-select", "true");
                    $(e.target).siblings().each(function()
                    {
                        let idd = $(this).attr("id");
                        if(idd)
                        {
                            $("#c"+idd).removeClass("displayFlex");
                        }
                    });
                    let tid = $(e.target).attr("id");
                    if(tid)
                    {
                        let yid = "c"+tid;
                        $("#"+yid).addClass("displayFlex");
                    }
              }
        });
        mainBox1_left_dom1.mouseleave(function(e)
        {
            $(e.target).closest('.productCategoryBox').find('*').removeClass('displayFlex');
        });

    function Search()
    {
        var selectVal = $("#id_select2_demo1").val();
        var key = $("#headerkey").val();
                var radio1 = $("#goods1").is(":checked");
        var radio3 = $("#goods2").is(":checked");
        // 构造查询字符串
        var queryParams = {
            key: key,
            inventory: radio1,
            roSh: radio3,
            page: 1,
            limit: 5
        };

        // 将查询参数对象转换为查询字符串
        var queryString = Object.keys(queryParams).map(function(key) {
            return encodeURIComponent(key) + '=' + encodeURIComponent(queryParams[key]);
        }).join('&');


        if(selectVal == 1)
        {
            window.location.href = '/Search/Index?'+queryString;
        }
        if(selectVal == 2)
        {
            //console.log(2,selectVal);
            window.location.href = '/Supplier/Index?Keys='+key;
        }
    }

    function Search2(key){
            var queryParams = {
            key: key,
            page: 1,
            limit: 5
        };
            // 将查询参数对象转换为查询字符串
    var queryString = Object.keys(queryParams).map(function(key) {
        return encodeURIComponent(key) + '=' + encodeURIComponent(queryParams[key]);
    }).join('&');
        window.location.href = '/Search/Index?'+queryString;
    }
    // 更新购物车数量的函数
    // 将函数定义在全局作用域中，使其在其他页面中可用
    window.updateCartCount = function(count) {
        var cartCountElement = document.getElementById('cartCount');
        if (cartCountElement) {
            cartCountElement.textContent = count;

            // 如果数量为0，隐藏数量标记
            if (count <= 0) {
                cartCountElement.style.display = 'none';
            } else {
                cartCountElement.style.display = 'flex';
            }
        }
    }
    window.translations = {
        featureNotAvailable: '@T("该功能暂未开放")',
        // 其他需要的翻译
    };
</script>

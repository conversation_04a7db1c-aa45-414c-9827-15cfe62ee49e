@media screen and (max-width: 1200px) {
    /* .productCategory {
        margin-left: 5% !important;
    } */

    /* .productCategory_hoverPages {
        width: 73vw !important;
    } */
}

/* 头部样式  -公共 */
.header {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid var(--line);
    width: 1440px;
    padding: 10px 3.6vw 16px 10vw;
    margin: 0 auto;

}

.headerItem {
    padding: 5px 10px;
    /* height: 1.8vw; */
    margin: 5px;
    font-size: 15px;
    display: flex;
    justify-content: space-between;
    place-items: center;
    position: relative;
}

.headerItem>.hoverText:hover {
    transition: all .3s;
    color: black;
    cursor: pointer;
}

.header_keyBox {
    position: absolute;
    top: 115%;
    left: 2%;
    padding-bottom: 10px;
    font-size: 12.5px;
    color: var(--text-color);
}

.header_key:hover {
    color: var(--blue-deep);
}

/* 关键字 input 用于判定是否选中 */
.header_keyCheck[type="radio"] {
    display: none;
}

/* 关键字 */
.header_key {
    padding: 0px 10px;
    font-size: var(--size0);
    cursor: pointer;
    transition: all .3s;
}

/* 当对应的单选按钮被选中时，改变菜单按钮的背景颜色 */
.header_keyCheck[type="radio"]:checked+.header_key {
    color: var(--blue);
}

.headLogo {
    max-width: 137px;
    width: 100%;

}

.searchBox {
    width: 100%;
    height: 34px;
    min-height: 30px;
    border: 2px solid var(--blue-deep);
    box-sizing: border-box;
    border-radius: 50px;
    /* overflow: hidden; */
    /* background-color: var(--bd); */
    display: flex;
    place-items: center;
}

.searchBoxItem {
    min-width: 600px;

}

.select2-search--dropdown {
    display: none !important;
}


.select2-results__option[aria-selected=true] {
    background: #e8eaeb;
}

/* 取消掉产品分类select的默认样式 */
/* .headerForm>.layui-form-select>.layui-select-title>.layui-input {
    all: unset;
    user-select: none;
    text-align: center;
    width: 90%;
    font-size: .9em;
}

.headerForm>.layui-form-select {
    margin-top: -3px;
    width: 5vw !important;
    min-width: 100px !important;
    user-select: none;
} */
/*  */
.select2-selection {
    border: none !important;
    background-color: transparent !important;
    outline: none !important;
    font-size: 12px !important;
}

/* 下拉框 */
.select2-dropdown {
    margin-top: -0px;
    min-width: 80px;
    /* padding: 5px 5px; */
    border: 1px solid rgb(214, 219, 221) !important;
    box-shadow: 0 0 2px #ebecec;
}

.select2-search__field {
    border: 1px solid #dee8e9 !important;
}

.searchInput {
    border: none !important;
    padding: 0px 10px;
    font-size: 14px;
    width: 100%;
    outline: none;
    box-shadow: none;
    color: var(--text-color);
    background-color: transparent;
}

.searchInput::placeholder {
    font-size: 13px !important;
    letter-spacing: 1px;
}

.searchButton {
    width: 25%;
    min-width: 80px;
    background-color: var(--blue-deep);
    height: 100%;
    border-radius: 0 15px 15px 0px;
    color: white;
    border: none;
}

.header2 {
    padding: 0 3.6vw 0 10vw;
    display: flex;
    place-items: center;
    background-color: var(--blue-deep);
    color: white;
    position: relative;
    overflow: visible;
    border-top: 1px solid var(--line-dark);
    width: 1440px;
    margin: 0 auto;


}

.header2>.headerForm>.layui-form-select>.layui-select-title>.layui-input {
    color: white;
}

.header2>div {
    padding: 4px;
    height: 80%;
    padding-left: 0;
}

/* header2 */
.header2_key:hover {
    color: var(--line);
}

.aa {
    width: 300px;
    display: flex;
    align-items: center;
}

/* 关键字 */
.header2_key {
    padding: 0px 10px;
    height: 40px !important;
    line-height: 40px;
    color: white;
    cursor: pointer;
    transition: all .3s;
}

#isPopup {
    margin-left: 42.4vw;
    position: relative;
    width: 254px;
    padding: .2vw 0;
    justify-content: space-between;
}

.lang {}

.currency {
    width: 150px;
    font-size: 13px;
    display: flex;
    justify-content: flex-end;
    margin: 0px -20px 0 0;

}

.currency img {
    margin: 0 8px 0 0;
}

.lngText {
    cursor: pointer;

}



.showLngBox {
    position: absolute;
    top: 140%;
    /* right: -10%; */
    min-width: 200px;
    min-height: 160px;
    width: 14vw !important;
    height: auto !important;
    max-height: 260px;
    background-color: white;
    z-index: 99999;
    border-radius: 15px;
    border: 1px solid #E2E3E9;
    box-shadow: 4px 2px 8px 0px rgba(0, 0, 0, 0.15), -4px -2px 8px 0px rgba(0, 0, 0, 0.15);
    padding: 1.5vw 1vw;
    color: var(--text-color);
}

/* .showLngBox>div {
    padding: 1vw 1vw;
} */

.showLngBox>div:nth-child(2n-1) {
    font-size: 1vw;
    text-indent: 20px;
}

.showLngBox>div:nth-child(2n) {
    padding: .35vw 0vw;
    margin-left: 20px;
}

.productCategory {
    user-select: none;
    position: relative;
    border-bottom: 1px solid transparent;
}

/* 取消掉产品分类四个字发光 */
.productCategory:hover .productCategoryBox {
    display: block;
}

/* 产品分类 开始 */
.productCategoryBox {
    position: absolute;
    top: 100%;
    left: 0%;
    width: 16vw;
    height: auto;
    min-height: 660px;
    max-height: 36vw;
    background-color: white;
    z-index: 99999;
    border-radius: 25px;
    /* display: block; */
    display: none;
}

.productCategory_onePage {
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: white;
    overflow-y: scroll;
    overflow-x: hidden;
    border-radius: 10px;
    z-index: 2;
}

.productCategory_onePage>div {
    padding: 15px 0px;
    display: flex;
    justify-content: space-between;
    color: var(--text-color);
    white-space: nowrap;
    text-align: left;
    font-size: 1em;
    text-indent: 1.7cqw;
}

/* 美化滚动条 */
.productCategory_onePage::-webkit-scrollbar {
    width: 4px;
}

.productCategory_onePage::-webkit-scrollbar-track {
    width: 6px;
    background: rgba(#101F1C, 0.1);
    -webkit-border-radius: 2em;
    -moz-border-radius: 2em;
    border-radius: 2em;
}

.productCategory_onePage::-webkit-scrollbar-thumb {
    background-color: #f2f2f2;
    background-clip: padding-box;
    min-height: 28px;
    -webkit-border-radius: 2em;
    -moz-border-radius: 2em;
    border-radius: 2em;
    transition: background-color .3s;
    cursor: pointer;
}

.productCategory_onePage::-webkit-scrollbar-thumb:hover {
    background-color: rgba(144, 147, 153, .3);
}

.productCategory_onePage:hover~.productCategory_hoverPages {
    /*display: flex;*/
}



.productCategory_hoverPages {
    position: relative;
    left: 100%;
    top: 0%;
    width: 65vw;
    min-height: 660px;
    max-height: 36vw;
    height: 100%;
    background-color: white;
    overflow: hidden;
    /* display: flex; */
    display: none;
    z-index: 2;
    border: 1px solid var(--line);
    border-radius: 0px 10px 10px 0px;
}

.productCategory_onePage>div>a:hover {
    color: var(--blue-deep);
}

.productCategory_hoverPages>div>div:hover {
    color: var(--blue-deep);
}

.productCategory_onePage:hover~.hoverPages {
    display: flex !important;

}

.productCategory_hoverPages:hover {
    display: flex;
}

.productCategory_hoverPages>div>div {
    padding: 16px 0px;
    display: flex;
    justify-content: space-between;
    color: var(--text-color);
    white-space: nowrap;
    text-align: left;
    font-size: 1em;
    text-indent: 1.7cqw;
}

.twoPage {
    flex: 3;
    overflow-y: scroll;
}

.twoPage>div[data-select="true"]>a {
    color: var(--blue-deep);
    /* background-color: var(--text-color4); */
}

.productCategory_onePage>div[data-select="true"] {
    color: var(--blue-deep);
}

.productCategory_onePage>div[data-select="true"]>a {
    color: var(--blue-deep);
    /* background-color: var(--text-color4); */
}

.threePageMore {
    position: absolute;
    top: 100px;
    right: 10vw;
    font-size: .9vw !important;
}

/* .goodsMore {
    width: 46vw;
    display: flex;
    justify-content: flex-end;
    flex-direction: row;
    height: 24px;
}

.goodsMore i {
    position: relative;
    top: 2px;
}

.mainBox1 .goodsMore {
    width: 52vw;
} */

a.jump {
    height: 4vw;
    width: 14vw;
    display: flex;
    align-items: center;
    margin-right: 2vw;
}

a.jump2 {
    height: 5vw;
    display: flex;
    align-items: center;
}

.threePage {
    margin-top: 1vw;
    flex: 9;
    z-index: 2;
    background-color: white;
    display: flex;
    align-content: flex-start;
    flex-wrap: wrap;
    overflow: hidden;
    border-radius: 0px 25px 25px 0px;

}



.threePage_contentBox {
    height: 3vw;
    width: 12vw;
    display: flex;
    place-items: center;
    white-space: nowrap;
    margin-right: 1.4vw;
}

.threePage_contentBox>img {
    margin: 0 2vw;
    max-width: 100px;
    height: 100%;
    object-fit: contain;
}


.threePage_contentBox_name {
    margin-left: 0px;
    width: 100%;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: keep-all;
    color: var(--blue-deep);
}

.threePage_contentBox_name>a {
    overflow: hidden;
}

.threePage_contentBox_name>a>p {
    color: var(--text-color);
}

/* 产品分类结束 */


/* z账户 */

.acountBox {
    color: var(--text-color);
    position: relative;
}

.acountBox:hover .acountInfo {
    display: flex;
}

.cartBox {
    color: var(--text-color);
    font-size: 1.1em;
    position: relative;
    padding: 5px 0;
    margin: 5px 0;
}


.unloginText {
    color: var(--gray-light) !important;
}

.acountInfo {
    position: absolute;
    top: 90%;
    right: -50px;
    width: 170px;
    padding: 1vw 1vw 0 1vw;
    border: 1px solid var(--line);
    box-shadow: 4px 2px 8px 0px rgba(0, 0, 0, 0.15);
    border-radius: 24px 24px 24px 24px;
    border: 1px solid #E2E3E9;
    display: flex;
    flex-direction: column;
    background-color: white;
    z-index: 999999;
    display: none;
}

.acountHead {
    padding: .2vw 0px 6px 0px;
    height: 100%;
    /* border-bottom: 1px solid var(--line); */
}

.acountHead>div:nth-child(1) {
    width: 25%;
    margin-right: auto;
}

.acountHead>div:nth-child(2) {
    width: 68%;
    /* border: 1px solid ; */
}

.acountHead>a>img {
    max-width: 50px;
    object-fit: contain;
}

.acountArea>div>.icon {
    margin-right: 4px;
    font-size: 20px !important;
}

.acountArea {
    margin: 5px 0;
    border-bottom: 1px solid var(--line);
}

.acountArea>div {
    padding: 6px 0px;
    font-size: 15px;
    display: flex;
    justify-content: left;
    place-items: center;
}

.acountArea>div:last-child {
    padding-bottom: 15px;
}

.acountArea>div:hover {
    color: var(--blue-deep);
    cursor: pointer;
}

.acountArea>div>div>a:hover {
    color: var(--blue-deep);
    cursor: pointer;
}

.acountArea>div>div:first-child {
    text-indent: 20px;
}

.acountArea>div>div:last-child {
    text-indent: .5vw;
}

.loginOutBox>a {
    display: block;
    padding: .5vw;
    border-radius: 20px;
    cursor: pointer;
    text-align: center;
    box-sizing: border-box;
    border: 1px solid transparent;
}

/* .loginOutBox>div:hover{
    background-color: var(--blue-deep);
    border: 1px solid var(--line);
    color: white;
} */
.loginOut {
    margin: 0 0 0 auto;
    color: var(--red);
    font-size: 14px;
    cursor: pointer;
}

.loginOut>a:hover {
    color: var(--red);
}

.acountHead[data-login="true"] {
    display: flex;
    /* display: none; */
}

.acountHead[data-login="false"] {
    display: none;
    /* display: flex; */
}


/* .loginOutBox>div{
    padding: 5px;
    border-radius: 20px;
    cursor: pointer;
    text-align: center;
    box-sizing: border-box;
    border: 1px solid transparent;
}
.loginOutBox>div:hover{
    background-color: var(--blue-deep);
    border: 1px solid var(--line);
    color: white;
}
.loginOut{
    margin-right: 10px;
    margin-left: auto;
    color: var(--red);
    text-decoration: underline;
    cursor: pointer;
}
.loginOut>a{
    color: var(--red);
} */
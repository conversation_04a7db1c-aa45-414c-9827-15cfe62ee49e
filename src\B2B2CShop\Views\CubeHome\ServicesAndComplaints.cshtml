﻿@* 服务投诉 *@
@{
  PekHtml.AppendCssFileParts("~/public/css/pageCss/service.css");
}
<div class="service">
  <ul class="top">
    <li>
      <img src="~/public/images/icons/dianhua.png" alt="">
      <div class="wen">
        <span>@T("在线客服热线")：</span>
        <span>0755-23152658</span>
      </div>
    </li>
    <li>
      <img src="~/public/images/icons/youxiang.png" alt="">
      <div class="wen">
        <span>@T("企业邮箱")：</span>
        <span><EMAIL></span>
      </div>

    </li>
    <li>
      <img src="~/public/images/icons/shijian.png" alt="">
      <div class="wen">
        <span>@T("服务时间")：</span>
        <span class="day">@T("工作日") 9:15~20:30</span>
        <span class="day">@T("节假日") 9:15~18:40</span>
      </div>
    </li>
  </ul>
  <main>
    <h3>@T("服务与投诉")</h3>
    <form class="layui-form">
      <div class="layui-row ">
        <div class="layui-col-md5 layui-col-xs5 layui-col-md-offset1 layui-col-xs-offset1">
          <div class="layui-form-item">
            <label class="layui-form-label">@T("姓名")</label>
            <div class="layui-input-block">
              <input type="text" name="Name" placeholder="@T("请输入姓名")" autocomplete="off" class="layui-input">
            </div>
          </div>
        </div>
        <div class="layui-col-md5 layui-col-xs5">
          <div class="layui-form-item">
            <label class="layui-form-label">@T("电话")</label>
            <div class="layui-input-block">
                            <input type="tel" name="Phone" autocomplete="off" lay-affix="clear" placeholder="@T("请输入电话")" class="layui-input demo-phone">
            </div>
          </div>
        </div>
      </div>
      <div class="layui-row">
        <div class="layui-col-md5 layui-col-xs5 layui-col-md-offset1 layui-col-xs-offset1">
          <div class="layui-form-item">
            <label class="layui-form-label">@T("邮箱")</label>
            <div class="layui-input-block">
              <input type="text" name="Mail" placeholder="@T("请输入邮箱")" autocomplete="off" class="layui-input">
            </div>
          </div>
        </div>
        <div class="layui-col-md5 layui-col-xs5">
          <div class="layui-form-item">
            <label class="layui-form-label">@T("公司名称")</label>
            <div class="layui-input-block">
              <input type="text" name="CompanyName" placeholder="@T("请输入公司名称")" autocomplete="off" class="layui-input">
            </div>
          </div>
        </div>
      </div>
      <div class="layui-row">
        <div class="layui-col-md5 layui-col-xs5 layui-col-md-offset1 layui-col-xs-offset1">
          <div class="layui-form-item">
            <label class="layui-form-label">@T("主题")</label>
            <div class="layui-input-block">
              <input type="text" name="Theme" placeholder="@T("请输入主题")" autocomplete="off" class="layui-input">
            </div>
          </div>
        </div>
        <div class="layui-col-md5 layui-col-xs5">
          <div class="layui-form-item">
            <label class="layui-form-label">@T("国家")</label>
            <div class="layui-input-block">
              <select name="Country" lay-search>
                  <option value="">@T("请选择国家")</option>
                  @foreach (var item in Model.Country)
                  {
                      <option value="@item.TwoLetterIsoCode">@item.Name</option>
                  }
              </select>
            </div>
          </div>
        </div>
      </div>
      <div class="layui-row">
        <div class="layui-col-md10 layui-col-md-offset1 layui-col-xs10 layui-col-xs-offset1">
          <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">@T("建议与反馈")</label>
            <div class="layui-input-block">
              <textarea id="myContent" placeholder="@T("请输入内容")" class="layui-textarea"></textarea>
            </div>
          </div>
        </div>
      </div>
      <div class="layui-row">
          <div class="layui-form-item btnBox">
            <button id="submitBtn" class="layui-btn btn" lay-submit lay-filter="demo1">确认</button>
          </div>
      </div>
    </form>
  </main>

</div>
<div class="bug"></div>
<script>
  document.addEventListener('DOMContentLoaded', function () {
    layui.use(['form', 'laydate', 'util'], function () {
      var form = layui.form;
      var layer = layui.layer;
      var laydate = layui.laydate;
      var util = layui.util;


      // 提交事件
      form.on('submit(demo1)', function (data) {
        var field = data.field; // 获取表单字段值

        field.Content = $('#myContent').val(); // 获取文本域内容

        // 1. 禁用按钮并显示 loading
        var btn = $('#submitBtn');
        btn.prop('disabled', true).html('<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i> @T("提交中")');

        $.post('@T("AddFeedback")',field,function(res){
            layui.layer.msg(res.msg);
            btn.prop('disabled', false).text('@T("确认")');

            if(res.success){
                window.location.reload();
            }
        })

        return false; // 阻止默认 form 跳转
      });
    });
  });


</script>
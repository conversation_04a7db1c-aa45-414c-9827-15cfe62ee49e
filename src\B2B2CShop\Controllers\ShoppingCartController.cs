﻿using Aop.Api.Domain;
using B2B2CShop.Areas.Member;
using B2B2CShop.Common;
using B2B2CShop.Dto;
using B2B2CShop.Entity;
using DH.Entity;
using iTextSharp.text;
using MailKit.Search;
using Mapster;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using MimeKit.Tnef;
using NewLife.Caching;
using NewLife.Cube;
using NewLife.Data;
using NewLife.Log;
using Newtonsoft.Json;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using PaypalServerSdk.Standard.Models;
using Pek;
using Pek.Helpers;
using Pek.Models;
using Pek.NCube;
using Pek.Seo;
using Pek.Timing;
using SixLabors.ImageSharp;
using System.Collections.Generic;
using System.ComponentModel;
using System.Dynamic;
using System.Security.Cryptography.X509Certificates;
using XCode.Membership;
using static SKIT.FlurlHttpClient.Wechat.TenpayV3.Models.CreateFundsToOverseaOrderRequest.Types;
using static SKIT.FlurlHttpClient.Wechat.TenpayV3.Models.CreateRateActivityApplicationRequest.Types.ActivityDetail.Types;
using Goods = B2B2CShop.Entity.Goods;
using Order = B2B2CShop.Entity.Order;

namespace B2B2CShop.Controllers;
/// <summary>
/// 购物车控制器
/// </summary>
[DHSitemap(IsUse = true)]
public class ShoppingCartController : PekBaseControllerX
{
    /// <summary>
    /// 分布式缓存提供者
    /// </summary>
    private readonly ICacheProvider _cacheProvider;

    public ShoppingCartController(ICacheProvider cacheProvider)
    {
        _cacheProvider = cacheProvider ?? throw new ArgumentNullException(nameof(cacheProvider));
    }

    public IActionResult Index()
    {
        dynamic viewModel = new ExpandoObject();
        //获取用户的购物车记录
        var carts = Cart.FindAllByBuyerId(SId);
        //获取供应商
        var stores = carts.GroupBy(c => c.StoreId).Select(s => new Store()
        {
            Id = s.First().StoreId,
            Name = GetResource(s.First().StoreName ?? "")
        });
        viewModel.cartList = carts;
        viewModel.storeList = stores;
        viewModel.lId = WorkingLanguage.Id;
        //获取商品相关推荐
        ViewBag.Randomlist = Goods.FindAllByRandomLan(6, WorkingLanguage.Id, WorkingCurrencies.ExchangeRate);
        return View(viewModel);
    }
    /// <summary>
    /// 修改购物车商品数量
    /// </summary>
    /// <param name="cartId"></param>
    /// <param name="num"></param>
    /// <returns></returns>
    [HttpPost]
    [DisplayName("修改购物车商品数量")]
    public Task<IActionResult> UpgradeGoodsNum(Int32 cartId,int num)
    {
        if(cartId.IsNull()) return Task.FromResult<IActionResult>(Json(new DResult { success = false, msg = GetResource("购物车信息为空") })); 
        if(num.IsNull()) return Task.FromResult<IActionResult>(Json(new DResult { success = false, msg = GetResource("数量为空") })); 
        var cart = Cart.FindById(cartId);
        if (cart == null) return Task.FromResult<IActionResult>(Json(new DResult { success = false, msg = GetResource("购物车信息为空") }));
        var merchantMaterial = MerchantMaterial.FindById(cart.MaterialId);
        if (merchantMaterial == null) return Task.FromResult<IActionResult>(Json(new DResult { success = false, msg = GetResource("商品物料为空") }));
        var count = merchantMaterial.Quantity - merchantMaterial.TemporaryQuantity;
        if(num > count) return Task.FromResult<IActionResult>(Json(new DResult { success = false, msg = GetResource("库存不足") }));
        cart.GoodsNum = num;
        var tieredPrice = GoodsTieredPrice.GetPriceByGoodsIdAndSkuIdAndQuantity(cart.GoodsId,cart.SKUId, num);
        if (tieredPrice==null)
        {
            var goods = Goods.FindById(cart.GoodsId);
            if (goods==null)
            {
                return Task.FromResult<IActionResult>(Json(new DResult { success = false, msg = GetResource("未查询到商品信息") }));
            }
            cart.GoodsPrice = goods.GoodsPrice;
            cart.Update();
            return Task.FromResult<IActionResult>(Json(new DResult { success = true, msg = GetResource("更新成功"), data = (goods.GoodsPrice*WorkingCurrencies.ExchangeRate).ToKeepTwoDecimal() }));
        }
        cart.GoodsPrice = tieredPrice.Price;
        cart.Update();
        return Task.FromResult<IActionResult>(Json(new DResult { success = true , msg = GetResource("更新成功"), data = (tieredPrice.Price * WorkingCurrencies.ExchangeRate).ToKeepTwoDecimal() }));
    }
    [HttpPost]
    [DisplayName("删除购物车")]
    public IActionResult DelCarts(string ids)
    {
        Cart.DelByIds(ids);
        int cartCount = Cart.FindAllByBuyerId(SId).Count;
        return Json(new DResult { success = true, data = cartCount, msg = GetResource("删除成功！") });
    }
    [HttpPost]
    [DisplayName("根据店铺ID删除购物车")]
    public IActionResult DelCartsByStoreId(Int64 storeId)
    {
        Cart.DelByStoreIdAndSID(storeId, SId);
        int cartCount = Cart.FindAllByBuyerId(SId).Count;
        return Json(new DResult { success = true, data = cartCount, msg = GetResource("删除成功！") });
    }
    [DisplayName("获取运费价格")]
    public IActionResult CalculateFreight(string countryCode, int shippingExpressId, string wareHouseIds,decimal weight)
    {
        if (countryCode.IsNullOrEmpty()) return Json(new DResult { success = false, msg = GetResource("国家编码为空") });
        if (shippingExpressId.IsNull()) return Json(new DResult { success = false, msg = GetResource("物流公司ID为空") });
        if (wareHouseIds.IsNullOrEmpty()) return Json(new DResult { success = false, msg = GetResource("仓库ID为空") });
        int[] _wareHouseIds = wareHouseIds.SplitAsInt(",");
        decimal freight = 0;
        int wareHouseId = 0;
        //查询有这些仓库的运费价格，取最优解
        for (int i = 0; i < _wareHouseIds.Length; i++)
        {
            var fentity = Freight.FindByLogisticsCompanyIdAndWareHouseIdAndCountryCode(shippingExpressId, _wareHouseIds[i], countryCode);
            if (freight==0)
            {
                freight = fentity?.ShippingCost ?? 0;
                wareHouseId = _wareHouseIds[i];
                if (weight > fentity.FirstWeight)
                {
                    freight += (weight - fentity.FirstWeight) * fentity.AdditionalWeight;//根据重量计算运费
                }
            }
            else
            {
                if (fentity != null)
                {
                    if (freight > fentity.ShippingCost)
                    {
                        freight = fentity.ShippingCost;
                        wareHouseId = _wareHouseIds[i];
                        if (weight > fentity.FirstWeight)
                        {
                            freight += (weight - fentity.FirstWeight) * fentity.AdditionalWeight;
                        }
                    }
                }
            }
        }
       
        freight = (freight * WorkingCurrencies.ExchangeRate).ToKeepTwoDecimal();
        return Json(new DResult { success = true, data = freight, extdata = wareHouseId });

    }
    [DisplayName("获取货运账户")]
    public IActionResult GetFreightAccount(int shippingExpressId)
    {
        if (shippingExpressId.IsNull()) return Json(new DResult { success = false, msg = GetResource("物流公司ID为空") });
        var freightAccoun = FreightAccount.FindByUIdAndExpressId(ManageProvider.User?.ID??0, shippingExpressId)?.AccountNumber??"";
        return Json(new DResult { success = true, data = freightAccoun });
    }

    /// <summary>
    ///提交订单页面
    /// </summary>
    /// <param name="CIds">购物车Id集合</param>
    /// <param name="GId">商品Id</param>
    /// <param name="SkuId">商品SkuId</param>
    /// <param name="Num">数量</param>
    /// <returns></returns>
    public IActionResult CheckOrder(String CIds,Int64 GId,Int32 Num,Int64 SkuId)
    {
        dynamic viewModel = new ExpandoObject();

        if (!CIds.IsNullOrWhiteSpace())//购物车结算
        {
            var listCart = Cart.FindAllByIds(CIds);

            var StoreIds = listCart.Select(e => e.StoreId);
            var StoreId = Store.Find(Store._.Id.In(StoreIds))?.Id;
            var StoreName = Store.Find(Store._.Id.In(StoreIds))?.Name;
            var merchNums = listCart.ToDictionary(e => e.MaterialId, e => e.GoodsNum);
            var wareHouseIds = WareHouseMaterial.FindAllWithAllMerchantMaterialIds(merchNums);//判断所有商品在同一个仓库是否有库存
            if (wareHouseIds.Count == 0) return Prompt(new PromptModel { Message = GetResource("仓库库存不足"),BackUrl=Url.Action("CheckOrder") });
            var list = listCart.Select(e => 
            {
                var goods = Goods.FindById(e.GoodsId);
                var skudetail = GoodsSKUDetail.FindById(e.SKUId);
                var tieredPriceModel = GoodsTieredPrice.GetPriceByGoodsIdAndSkuIdAndQuantity(e.GoodsId, e.SKUId, e.GoodsNum);
                var realPrice = tieredPriceModel?.Price ?? goods.GoodsPrice;//实际价格
                return new GoodsOrderDto
                {
                    GoodsId = goods?.Id ?? 0,
                    GoodsName = goods?.Name ?? "",
                    GoodsImage = AlbumPic.FindByNameAndSId(goods?.GoodsImage ?? "", goods?.StoreId ?? 0)?.Cover ?? "",
                    GoodsNum = e.GoodsNum,
                    MerchantMaterial = e.MaterialId,
                    Price = realPrice,
                    GoodsPrice = (realPrice * WorkingCurrencies.ExchangeRate).ToKeepTwoDecimal(),
                    SKUId = e.SKUId,
                    Spec = skudetail?.SpecValueDetail(WorkingLanguage.Id) ?? "",
                    TotalWeight = (goods?.GoodsWeight ?? 0) * e.GoodsNum
                };
            }).ToList();

            viewModel.weight = list.Sum(e=>e.TotalWeight);
            viewModel.storeId = StoreId;
            viewModel.storeName = StoreName;//店铺名称
            viewModel.data = list;//商品列表
            viewModel.totalPrice = list.Sum(e=>e.GoodsNum * e.GoodsPrice);//商品总价
        }
        else
        {
            if (GId<=0)
            {
                return Prompt(new PromptModel { Message = GetResource("商品不存在") });
            }
            if (Num <= 0)
            {
                return  Prompt(new PromptModel { Message = GetResource("商品数量不合法") });
            }
            var goods = Goods.FindById(GId);
            if (goods == null)  return Prompt(new PromptModel { Message = GetResource("商品不存在") });

            var skudetail = GoodsSKUDetail.FindById(SkuId);

            long materialId = skudetail?.MaterialId ?? goods.MerchantMaterial;
            var material = MerchantMaterial.FindById(materialId);
            if (material == null) return Prompt(new PromptModel() { Message = GetResource("商品物料不存在") });
            //判断库存数量
            var werehouseMaterial = WareHouseMaterial.FindAllByMaterialIdLan(materialId, WorkingLanguage.Id).OrderBy(o => o.Quantity).FirstOrDefault();//获取数量最多的仓库
            if (werehouseMaterial == null) return Prompt(new PromptModel { Message = GetResource("仓库库存不足") });

            if (werehouseMaterial.Quantity < Num) return Prompt(new PromptModel { Message = GetResource("仓库库存不足") });

            List<GoodsOrderDto> list = new();
            var tieredPriceModel = GoodsTieredPrice.GetPriceByGoodsIdAndSkuIdAndQuantity(goods.Id,SkuId, Num);
            decimal realPrice = 0;//实际价格
            if (tieredPriceModel == null)
            {
                realPrice = goods.GoodsPrice;//取物料价格
            }
            else
            {
                realPrice = tieredPriceModel.Price;
            }
            list.Add(new GoodsOrderDto
            {
                GoodsId = goods.Id,
                GoodsName = goods.Name,
                GoodsImage = AlbumPic.FindByNameAndSId(goods.GoodsImage, goods.StoreId)?.Cover ?? "",
                GoodsNum = Num,
                MerchantMaterial = materialId,
                Price = realPrice,
                GoodsPrice = (realPrice * WorkingCurrencies.ExchangeRate).ToKeepTwoDecimal(),
                SKUId = SkuId,
                Spec = skudetail?.SpecValueDetail(WorkingLanguage.Id) ?? "",
                TotalWeight = (goods.GoodsWeight) * Num
            });
            var StoreName = Store.FindById(goods.StoreId)?.Name;
            viewModel.weight = list.Sum(e => e.TotalWeight);
            viewModel.storeId = goods.StoreId;
            viewModel.storeName = StoreName;//店铺名称
            viewModel.data = list;//商品列表
            viewModel.totalPrice = list.Sum(e => e.GoodsNum * e.GoodsPrice);//商品总价
        }
        var addressList = BuyerAddress.FindAllByUId(ManageProvider.User?.ID ?? 0);
        viewModel.deliveryAddress = addressList.Where(e => e.AddressType != 1).OrderByDescending(a => a.IsDefaultDelivery).ToList();//收货地址
        viewModel.InvoiceAddress = addressList.Where(e => e.AddressType != 0).OrderByDescending(a => a.IsDefaultInvoice).ToList();//发票地址
        viewModel.expressList = InternationalExpress.FindAllLanEnable(WorkingLanguage.Id);//快递公司
        viewModel.CIds = CIds;
        viewModel.GId = GId;
        viewModel.Num = Num;
        viewModel.SkuId = SkuId;
        return View(viewModel);
    }

    /// <summary>
    /// 查询快递运费
    /// </summary>
    /// <param name="DId">地址Id</param>
    /// <param name="weight">重量</param>
    /// <returns></returns>
    [HttpPost]
    public IActionResult QueryFreightList(Int32 DId,decimal weight, List<GoodsOrderDto> Pamrs)
    {
        DResult res = new();
        var address = BuyerAddress.FindById(DId);
        if(address == null)
        {
            res.msg = GetResource("收货地址为空");
            return Json(res);
        }
        if(Pamrs == null)
        {
            res.msg = GetResource("商品信息为空");
            return Json(res);
        }
        List<Int32> WIds = new();//有库存的仓库Id
        foreach (var item in Pamrs)
        {
            var listWareHouseMaterial = WareHouseMaterial.FindAllByMerchantMaterialId(item.MerchantMaterial);
            foreach (var WareHouseMaterial in listWareHouseMaterial)
            {
                //暂扣
                var zk = TemporaryInventory.FindAllByWIdAndMId(WareHouseMaterial.WareHouseId,item.MerchantMaterial).Sum(e => e.Quantity);
                if (item.GoodsNum <= WareHouseMaterial.Quantity - zk)
                {
                    WIds.Add(WareHouseMaterial.WareHouseId);
                }
            }
        }
        if (WIds.Count == 0)
        {
            res.msg = GetResource("库存不足");
            return Json(res);
        }

        var reslist = WIds.GroupBy(e => e).Select(e => new { Id = e.Key, Count = e.Count() }).ToList();

        var max = reslist.Max(e => e.Count);

        var id = reslist.FirstOrDefault(e=>e.Count == max)?.Id ?? 0;

        WIds = WIds.Where(e => e == id).ToList();//取数量最多的仓库

        var expressList = InternationalExpress.FindAllLanEnable(WorkingLanguage.Id);//快递公司
        var listFreight = expressList.Select(e => 
        {
            var freight = Freight.FindByEIdAndCodeAndWId(e.Id, address.TwoLetterIsoCode ?? "",WIds);
            decimal shippingCost = freight?.ShippingCost ?? 0;//运费
            //计算运费，超过首重的部分按0.5kg计算
            if (weight - (freight?.FirstWeight??0)>0)
            {
                shippingCost += (int)Math.Ceiling((weight - (freight?.FirstWeight ?? 0))/((decimal)0.5)) * (freight?.AdditionalWeight ?? 0);
            }
            shippingCost =(shippingCost *  WorkingCurrencies.ExchangeRate).ToKeepTwoDecimal();//汇率
            return new
            {
                LogisticsCompanyId = e.Id,
                ShippingCost = shippingCost
            };
        });
        res.success = true;
        res.data = listFreight;
        res.extdata = WIds.Where(e=>e == id);
        return Json(res);
    }

    /// <summary>
    /// 提交订单
    /// </summary>
    /// <param name="Pamrs">商品列表</param>
    /// <param name="EId">快递公司Id</param>
    /// <param name="AId1">收货地址Id</param>
    /// <param name="AId2">发票地址Id</param>
    /// <param name="Account">配送账户</param>
    /// <param name="WIds">仓库id集合</param>
    /// <param name="SId">店铺Id</param>
    /// <returns></returns>
    [HttpPost]
    public IActionResult SubmitOrder(List<GoodsOrderDto> Pamrs, Int32 EId,Int32 AId1, Int32 AId2, String Account,Int64 SId,List<Int32> WIds)
    {
        DResult res = new();
        var uId = ManageProvider.User?.ID ?? 0;
        if (uId == 0)
        {
            res.msg = GetResource("请先登录");
            return Json(res);
        }
        var modelStore = Store.FindById(SId);
        if(modelStore == null)
        {
            res.msg = GetResource("店铺信息为空");
            return Json(res);
        }
        var modelBuyerAddress1 = BuyerAddress.FindById(AId1);
        if (modelBuyerAddress1 == null)
        {
            res.msg = GetResource("收货地址为空");
            return Json(res);
        }
        var modelBuyerAddress2 = BuyerAddress.FindById(AId2);
        if (modelBuyerAddress2 == null)
        {
            res.msg = GetResource("发票地址为空");
            return Json(res);
        }
        var shipExpress = InternationalExpress.FindById(EId);
        if (shipExpress == null)
        {
            res.msg = GetResource("物流地址为空");
            return Json(res);
        }
        var modelFreight = Freight.FindByLogisticsCompanyIdAndCountryCode(EId, modelBuyerAddress1.TwoLetterIsoCode ?? "");//根据收货地址和快递公司查询运费对象，
        if (modelFreight == null && Account.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("对应区域未设置运费");
            return Json(res);
        }
        if(WIds.Count == 0)
        {
            res.msg = GetResource("库存不足");
            return Json(res);
        }
        foreach (var item in Pamrs)
        {
            // 使用分布式锁防止并发超卖
            var lockKey = $"B2B2CShop:inventory_lock:{item.MerchantMaterial}";
            var processingKey = $"B2B2CShop:inventory_processing:{item.MerchantMaterial}:"; // 正在处理中的缓存键
            var completedKey = $"B2B2CShop:inventory_completed:{item.MerchantMaterial}_{uId}"; // 已完成处理的缓存键

            using var distributedLock = _cacheProvider.AcquireLock(lockKey, 3_000); // 3秒超时
            if (distributedLock == null)
            {
                res.msg = GetResource("系统繁忙，请稍后重试");
                return Json(res);
            }

            // 设置处理中标记，防止重复处理
            var setResult = _cacheProvider.Cache.Add(processingKey, "1", 300); // 5分钟过期
            if (!setResult)
            {
                XTrace.WriteLine("当前物料抢购人数过多");
                res.msg = GetResource("系统繁忙，请稍后重试");
                return Json(res);
            }

            try
            {
                // 检查是否已经完成处理
                if (_cacheProvider.Cache.ContainsKey(completedKey))
                {
                    res.msg = GetResource("请勿重复提交同物料订单");
                    return Json(res);
                }

                var modelWareHouseMaterial = WareHouseMaterial.FindByWIdsAndMId(WIds, item.MerchantMaterial);
                if (modelWareHouseMaterial == null)
                {
                    res.msg = GetResource("库存不足");
                    return Json(res);
                }

                var zk = TemporaryInventory.FindAllByWIdAndMId(modelWareHouseMaterial.WareHouseId, modelWareHouseMaterial.MerchantMaterialId).Sum(e => e.Quantity);
                if (item.GoodsNum > modelWareHouseMaterial.Quantity - zk)
                {
                    res.msg = GetResource("库存不足");
                    return Json(res);
                }

                // 预扣库存成功后，设置已完成缓存
                _cacheProvider.Cache.Set(completedKey, "1", 1); // 1秒过期，同一用户在1秒内只能提交一次同物料订单
            }
            finally
            {
                // 清除正在处理状态
                _cacheProvider.Cache.Remove(processingKey);
            }
        }
        using (var tran = Order.Meta.CreateTrans())
        {
            decimal shippingCost = 0;
            if (Account.IsNullOrEmpty())
            {
                decimal weight = Pamrs.Sum(e => e.TotalWeight);//订单商品总重量
                shippingCost = modelFreight?.ShippingCost ?? 0;//运费
                if (weight - (modelFreight?.FirstWeight ?? 0) > 0)//计算运费，超过首重的部分按0.5kg计算
                {
                    shippingCost += (int)Math.Ceiling((weight - (modelFreight?.FirstWeight ?? 0)) / ((decimal)0.5)) * (modelFreight?.AdditionalWeight ?? 0);
                }
            }
            else
            {
                //判断有没有货运账户
                var freightAccount = FreightAccount.FindByUIdAndExpressId(uId, EId);
                if (freightAccount == null)
                {
                    freightAccount = new FreightAccount();
                    freightAccount.UId = uId;
                    freightAccount.ExpressId = EId;
                    freightAccount.ExpressCode = shipExpress.Key;
                }
                //添加货运账户表
                freightAccount.AccountNumber = Account;
                freightAccount.Save();
            }

            //添加订单表
            var modelOrder = new Order()
            {
                OrderSn = Randoms.MakeFileRndName(),
                StoreId = modelStore.Id,
                StoreName = modelStore.Name,
                BuyerId = uId,
                BuyerName = ManageProvider.User?.Name,
                BuyerEmail = ManageProvider.User?.Mail,
                AddTime = UnixTime.ToTimestamp(),
                CurrencyCode = WorkingCurrencies.Code,//订单货币代码
                CurrencyRate = WorkingCurrencies.ExchangeRate,//订单货币汇率
                SymbolLeft = WorkingCurrencies.SymbolLeft,//左标志
                SymbolRight = WorkingCurrencies.SymbolRight,//右标志
                CurGoodsAmount = Pamrs.Sum(e => e.GoodsNum * e.GoodsPrice),
                GoodsAmount = Pamrs.Sum(e => e.GoodsNum * e.Price),
                CurShippingFee = (shippingCost*WorkingCurrencies.ExchangeRate).ToKeepTwoDecimal(),
                ShippingFee = shippingCost,
                OrderRefundLockState = 0,
                OrderFrom = "PC",
                OrderType = 0,
                OrderState = 10,
            };
            modelOrder.OrderAmount = modelOrder.GoodsAmount + modelOrder.ShippingFee;
            modelOrder.CurOrderAmount = modelOrder.CurGoodsAmount + modelOrder.CurShippingFee;
            modelOrder.Insert();

            res.data = modelOrder.Id.SafeString();

            var deliveryaddressDto = modelBuyerAddress1.Adapt<AddressInfoDto>();
            deliveryaddressDto.Name = modelBuyerAddress1.RealName;

            var invoiceAddressDto = modelBuyerAddress2.Adapt<AddressInfoDto>();
            invoiceAddressDto.Name = modelBuyerAddress1.RealName;

            //添加订单公共信息表
            var modelOrderCommon = new OrderCommon()
            {
                Id = modelOrder.Id,
                StoreId = modelOrder.StoreId,
                ShippingExpressId = EId,
                ReciverName = modelBuyerAddress1.RealName,
                Phone = modelBuyerAddress1.Phone,
                ZipCode = modelBuyerAddress1.ZipCode,
                ReciverCountryId = modelBuyerAddress1.CountryId,
                ReciverTwoLetterIsoCode = modelBuyerAddress1.TwoLetterIsoCode,
                ReciverProvinceId = modelBuyerAddress1.RegionId,
                ReciverCityId = modelBuyerAddress1.CityId,
                ReciverInfo = JsonConvert.SerializeObject(deliveryaddressDto),
                InvoiceInfo = JsonConvert.SerializeObject(invoiceAddressDto),
            };
            //modelOrderCommon.DaddressId = 0;
            modelOrderCommon.Insert();

            foreach (var item in Pamrs)
            {
                var modelGoods = Goods.FindById(item.GoodsId);
                if(modelGoods != null) 
                {
                    //添加订单商品表
                    var modelOrderGoods = new OrderGoods()
                    {
                        OrderId = modelOrder.Id,
                        GoodsId = modelGoods.Id,
                        GoodsName = GoodsLan.FindByGIdAndLId(modelGoods.Id, WorkingLanguage.Id)?.LanName ?? modelGoods.Name,
                        GoodsPrice = item.Price,
                        CurGoodsPrice = item.GoodsPrice,
                        GoodsNum = item.GoodsNum,
                        GoodsImage = GoodsLan.FindByGIdAndLId(modelGoods.Id, WorkingLanguage.Id)?.LanGoodsImage ?? modelGoods.GoodsImage,
                        GoodsPayPrice = item.Price,
                        CurGoodsPayPrice = item.GoodsPrice,
                        SymbolLeft = WorkingCurrencies.SymbolLeft,
                        StoreId = modelGoods.StoreId,
                        BuyerId = modelOrder.BuyerId,
                        GoodsType = 1,
                        GcId = modelGoods.CId,
                        SKUId = item.SKUId,
                        MaterialId = item.MerchantMaterial,
                    };
                    modelOrderGoods.Insert();

                    var modelMerchantMaterial = MerchantMaterial.FindById(item.MerchantMaterial);

                    if(modelMerchantMaterial != null)
                    {
                        //暂扣库存
                        modelMerchantMaterial.TemporaryQuantity += item.GoodsNum;
                        modelMerchantMaterial.Update();

                        var modelTemporaryInventory = new TemporaryInventory()
                        {
                            WareHouseId = WIds[0],
                            OrderId = modelOrder.Id,
                            OrderGoodsId = modelOrderGoods.Id,
                            MerchantMaterialId = modelMerchantMaterial.Id,
                            StoreId = modelOrderGoods.StoreId,
                            Quantity = modelOrderGoods.GoodsNum,
                        };
                        modelTemporaryInventory.Insert();
                    }
                }
            }

            tran.Commit();
        }
        res.success = true;
        
        return Json(res);
    }

    /// <summary>
    /// 根据购物车Id和商品Id修改数量
    /// </summary>
    /// <param name="CIds">购物车id集合</param>
    /// <param name="GoodsId">商品id</param>
    /// <param name="Num">数量</param>
    /// <returns></returns>
    [HttpPost]
    public IActionResult FindByCraIdsAndGoodsIdUpdateGoodsNum(String CIds,Int64 GoodsId,Int32 Num)
    {
        DResult res = new();

        if (CIds.IsNullOrWhiteSpace())
        {
            res.msg = GetResource("购物车信息为空");
            return Json(res);
        }

        if(Num <= 0)
        {
            res.msg = GetResource("商品数量不能为空");
            return Json(res);
        }

        var model = Cart.Find(Cart._.Id.In(CIds.Split(",")) & Cart._.GoodsId == GoodsId);

        if(model == null)
        {
            res.msg = GetResource("购物车信息不存在");
            return Json(res);
        }

        var sum = WareHouseMaterial.FindAllByMaterialIdLan(model.MaterialId, WorkingLanguage.Id).Sum(e => e.Quantity);
        if (Num > sum)
        {
            res.msg = GetResource("商品库存不足");
            return Json(res);
        }

        var listCart = Cart.FindAllByIds(CIds);

        var list = new List<Cart>();
        foreach (var item in listCart)
        {
            var cart = new Cart() { MaterialId = item.MaterialId };
            if (item.GoodsId == GoodsId) cart.GoodsNum = Num;
            else cart.GoodsNum = item.GoodsNum;
            list.Add(cart);
        }
        var merchNums = list.ToDictionary(e => e.MaterialId, e => e.GoodsNum);
        var wareHouseIds = WareHouseMaterial.FindAllWithAllMerchantMaterialIds(merchNums);//判断所有商品在同一个仓库是否有库存
        if (wareHouseIds.Count == 0)
        {
            res.msg = GetResource("仓库库存不足");
            return Json(res);
        }

        model.GoodsNum = Num;
        model.Update();
        res.success = true;
        res.msg = GetResource("更新成功");
        return Json(res);
    }

    /// <summary>
    /// 验证商品库存
    /// </summary>
    /// <param name="GId"></param>
    /// <param name="Num"></param>
    /// <param name="SkuId"></param>
    /// <returns></returns>
    public IActionResult verifyGoods(Int64 GId,Int32 Num,Int64 SkuId)
    {
        DResult res = new();

        if (Num <= 0)
        {
            res.msg = GetResource("商品数量不能为空");
            return Json(res);
        }

        var mid = SkuId > 0 ? GoodsSKUDetail.FindById(SkuId)?.MaterialId : Goods.FindById(GId)?.MerchantMaterial;   

        var werehouseMaterial = WareHouseMaterial.FindAllByMaterialIdLan(mid ?? 0, WorkingLanguage.Id).OrderBy(o => o.Quantity).FirstOrDefault();//获取数量最多的仓库

        if (werehouseMaterial == null)
        {
            res.msg = GetResource("仓库库存不足");
            return Json(res);
        }
        if (werehouseMaterial.Quantity < Num)
        {
            res.msg = GetResource("商品库存不足");
            return Json(res);
        }
        res.success = true;
        return Json(res);
    }
}
